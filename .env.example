DATABASE_URL="postgres://<user>:<password>@<url>:<port>/<db_name>"


NEXT_PUBLIC_APP_URL="required"

GOOGLE_CLIENT_ID="optional"
GOOGLE_CLIENT_SECRET="optional"

GITHUB_CLIENT_ID="optional"
GITHUB_CLIENT_SECRET="optional"

LINKEDIN_CLIENT_ID="optional"
LINKEDIN_CLIENT_SECRET="optional"

BETTER_AUTH_SECRET="required"

RESEND_API_KEY="required"

R2_ENDPOINT="https://YOUR-ID.r2.cloudflarestorage.com"
R2_ACCESS_KEY_ID="your-r2-access-key-id"
R2_SECRET_ACCESS_KEY="your-r2-secret-access-key"
R2_BUCKET_NAME="your-r2-bucket-name"
R2_PUBLIC_URL="https://your-custom-domain.com"

CREEM_ENVIRONMENT=test_mode
CREEM_API_KEY=creem_test_api_key_here
CREEM_WEBHOOK_SECRET=whsec_test_secret_here