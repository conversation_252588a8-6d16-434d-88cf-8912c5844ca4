{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext", "ES2020"], "types": ["node"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/env": ["./env.mjs"], "@/keystatic.config": ["./keystatic.config.ts"], "@/styles/*": ["./styles/*"], "env": ["./env.mjs"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}