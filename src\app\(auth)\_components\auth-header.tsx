"use client"

import type React from "react"

import Link from "next/link"
import { ModeToggle } from "@/components/mode-toggle"
export default function AuthHeader() {
    return (
        <>
            {/* Background gradient elements */}
            <div className="absolute top-0 right-0 w-full h-full bg-gradient-to-bl from-gray-800/20 to-transparent" />

            {/* Back to Home link */}
            <Link
                href="/"
                className="absolute top-6 left-6 text-muted-foreground hover:text-foreground transition-colors flex items-center gap-2"
            >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="m15 18-6-6 6-6" />
                </svg>
                Home
            </Link>

            <div className="absolute top-6 right-6">
                <ModeToggle />
            </div>
        </>
    );
}
