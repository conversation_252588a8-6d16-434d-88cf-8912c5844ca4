import env from "@/env";

const engine = env.DATABASE_ENGINE; // 'postgres' | 'mysql' | 'sqlite'

/**
 * Detects if the application is running in a serverless environment
 */
function isServerlessEnvironment(): boolean {
  return Boolean(
    process.env.VERCEL ||
      process.env.AWS_LAMBDA_FUNCTION_NAME ||
      process.env.NETLIFY ||
      process.env.RAILWAY_ENVIRONMENT ||
      process.env.FUNCTIONS_EMULATOR ||
      process.env.AZURE_FUNCTIONS_ENVIRONMENT,
  );
}

/**
 * Gets the appropriate database connection configuration based on the environment
 */
export function getConnectionConfig() {
  const isServerless = isServerlessEnvironment();
  if (isServerless) {
    // keep a minimal single-connection config, but shape it per engine
    if (engine === "postgres") {
      return {
        // drizzle/postgres-js expects these names
        max: 1,
        idle_timeout: 20,
        max_lifetime: 60 * 30,
        connect_timeout: 30,
        prepare: true,
        onnotice: () => {},
      };
    }

    if (engine === "mysql") {
      // mysql2 pool options; include 'max' for backward compatibility
      return {
        max: 1,
        connectionLimit: 1,
        waitForConnections: true,
        queueLimit: 0,
        connectTimeout: 30 * 1000, // milliseconds
      };
    }

    // sqlite has no pool; return a minimal config with max=1 for validation
    return { max: 1 };
  }

  // Traditional server environment
  if (engine === "postgres") {
    return {
      max: env.DATABASE_POOL_SIZE,
      idle_timeout: env.DATABASE_IDLE_TIMEOUT,
      max_lifetime: env.DATABASE_MAX_LIFETIME,
      connect_timeout: env.DATABASE_CONNECT_TIMEOUT,
      prepare: true,
      debug: process.env.NODE_ENV === "development",
      onnotice: process.env.NODE_ENV === "development" ? console.log : () => {},
    };
  }

  if (engine === "mysql") {
    return {
      // keep 'max' for validateDatabaseConfig compatibility
      max: env.DATABASE_POOL_SIZE,
      connectionLimit: env.DATABASE_POOL_SIZE,
      waitForConnections: true,
      queueLimit: 0,
      // mysql2 expects ms
      connectTimeout: env.DATABASE_CONNECT_TIMEOUT * 1000,
      // expose debug flag similarly
      debug: process.env.NODE_ENV === "development",
    };
  }

  // sqlite or unknown engines: minimal options
  return {
    max: env.DATABASE_POOL_SIZE || 1,
  };
}

/**
 * Gets the current environment type for logging and monitoring
 */
export function getEnvironmentType(): "serverless" | "traditional" {
  return isServerlessEnvironment() ? "serverless" : "traditional";
}

let configValidated = false;

/**
 * Validates the database configuration
 */
export function validateDatabaseConfig() {
  if (configValidated) {
    return;
  }

  const config = getConnectionConfig();
  const envType = getEnvironmentType();

  console.log(`Database configuration loaded for ${envType} environment.`);

  if (envType === "serverless" && config.max && config.max > 2) {
    console.warn(
      "Warning: High connection pool size detected in serverless environment",
    );
  }

  if (envType === "traditional" && config.max && config.max < 5) {
    console.warn(
      "Warning: Low connection pool size for traditional server environment",
    );
  }

  configValidated = true;
}

/**
 * Resets the configuration validation flag (for testing purposes)
 */
export function resetConfigValidation() {
  configValidated = false;
}
