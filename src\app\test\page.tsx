"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { SquareLogo } from "@/components/brand/square-logo"
import { Eye, EyeOff } from "lucide-react"
import AuthHeader from "@/app/(auth)/_components/auth-header"

export default function SignupPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  // const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle signup logic here
    console.log("Signup attempt:", { email, password })
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center relative overflow-hidden" >

      <AuthHeader />

      <div className="w-full max-w-lg pt-8 relative z-10 sm:px-0 px-6">
        <div className="text-center mb-6">
          <SquareLogo className="mb-6" />
          <h1 className="text-2xl font-semibold text-foreground">Create an account</h1>
          <p className="text-muted-foreground">
            {"Already have an account? "}
            <Link href="/login" className="text-foreground hover:underline">
              Log in
            </Link>
            .
          </p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button
              variant="secondary"
              className="dark:bg-[linear-gradient(104deg,rgba(253,253,253,0.05)_5%,rgba(240,240,228,0.1)_100%)] hover:shadow-[0_0_20px_rgba(0,0,0,0.3)] dark:border-none dark:hover:bg-white dark:hover:border-white hover:border-primary hover:bg-primary hover:text-white dark:hover:text-black dark:hover:shadow-[0_0_20px_rgba(255,255,255,0.3)] shadow-none dark:shadow border-[2px] h-12 text-sm rounded-xl transition-all duration-200"
            >
              <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Login with Google
            </Button>

            <Button
              variant="secondary"
              className="dark:bg-[linear-gradient(104deg,rgba(253,253,253,0.05)_5%,rgba(240,240,228,0.1)_100%)] hover:shadow-[0_0_20px_rgba(0,0,0,0.3)] dark:border-none dark:hover:bg-white dark:hover:border-white hover:border-primary hover:bg-primary hover:text-white dark:hover:text-black dark:hover:shadow-[0_0_20px_rgba(255,255,255,0.3)] shadow-none dark:shadow border-[2px] h-12 text-sm rounded-xl transition-all duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
              </svg>
              Login with GitHub
            </Button>
          </div>
        </div>

        <div className="mb-6 mt-6 flex items-center justify-center">
          <div aria-hidden="true" className="w-full border-t dark:border-border border-gray-300" data-orientation="horizontal" role="separator"></div>
          <span className="text-sm text-muted-foreground font-normal mx-4">or</span>
          <div aria-hidden="true" className="w-full border-t dark:border-border border-gray-300" data-orientation="horizontal" role="separator">
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-foreground">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="relative w-full select-none appearance-none transition ease-in-out duration-200 disabled:cursor-not-allowed disabled:opacity-70 disabled:text-gray-500 text-base sm:text-sm text-gray-900 dark:text-white shadow-none dark:hover:shadow-[0_0_20px_rgba(255,255,255,0.3)] border-[2px] border-gray-200 dark:border-white/5 dark:hover:border-white backdrop-blur-[25px] bg-origin-border bg-[linear-gradient(104deg,rgba(255,255,255,0.6)_5%,rgba(245,245,245,0.8)_100%)] dark:bg-[linear-gradient(104deg,rgba(253,253,253,0.05)_5%,rgba(240,240,228,0.1)_100%)] focus-visible:ring-1 focus-visible:ring-primary dark:focus-visible:ring-white/10 focus-visible:outline-hidden h-12 !rounded-2xl px-4"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-foreground">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="●●●●●●●●●"
                className="relative w-full select-none appearance-none transition ease-in-out duration-200 disabled:cursor-not-allowed disabled:opacity-70 disabled:text-gray-500 text-base sm:text-sm text-gray-900 dark:text-white shadow-none dark:hover:shadow-[0_0_20px_rgba(255,255,255,0.3)] border-[2px] border-gray-200 dark:border-white/5 dark:hover:border-white backdrop-blur-[25px] bg-origin-border bg-[linear-gradient(104deg,rgba(255,255,255,0.6)_5%,rgba(245,245,245,0.8)_100%)] dark:bg-[linear-gradient(104deg,rgba(253,253,253,0.05)_5%,rgba(240,240,228,0.1)_100%)] focus-visible:ring-1 focus-visible:ring-gray-100 dark:focus-visible:ring-white/10 focus-visible:outline-hidden h-12 !rounded-2xl px-4"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <Button
            type="submit"
            className="mt-1 w-full bg-primary text-primary-foreground hover:bg-primary dark:hover:bg-white dark:hover:text-black hover:shadow-[0_0_20px_rgba(0,0,0,0.3)] dark:hover:shadow-[0_0_20px_rgba(255,255,255,0.3)] h-12 rounded-xl transition-all duration-200"
          >
            Create Account
          </Button>
        </form>

        <div className="mt-8 text-center text-xs text-muted-foreground">
          By creating an account, you agree to our{" "}
          <Link href="/terms" className="hover:text-foreground">
            Terms
          </Link>{" "}
          and{" "}
          <Link href="/privacy" className="hover:text-foreground">
            Privacy Policy
          </Link>
          .
        </div>
      </div>
    </div>
  )
}