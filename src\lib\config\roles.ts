import { userRoleEnum } from "@/database/schema";

export type UserRole = (typeof userRoleEnum.enumValues)[number];

export const ROLE_HIERARCHY: Record<UserRole, number> = {
  user: 1,
  admin: 2,
  super_admin: 3,
} as const;

export function hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
}

export function getAllRoles(): UserRole[] {
  return userRoleEnum.enumValues as UserRole[];
}

export function getRoleLevel(role: UserRole): number {
  return ROLE_HIERARCHY[role];
}

export function isAdminRole(role: UserRole): boolean {
  return hasRole(role, "admin");
}

export function isSuperAdminRole(role: UserRole): boolean {
  return role === "super_admin";
}
