import type { TableConfig } from "./types";
import type { EnabledTableKeys } from "@/lib/config/admin-tables";

export const adminTableConfig: Partial<Record<EnabledTableKeys, TableConfig>> =
  {
    uploads: {
      userRelated: "userId",
      hiddenColumns: ["token"],
      readOnlyColumns: ["id", "createdAt"],
    },
  };

export function getTableConfig(tableName: EnabledTableKeys): TableConfig {
  return (
    adminTableConfig[tableName] || {
      userRelated: false,
      hiddenColumns: [],
      readOnlyColumns: ["id", "createdAt", "updatedAt"],
    }
  );
}

export function isUserRelatedTable(tableName: EnabledTableKeys): boolean {
  const config = getTableConfig(tableName);
  return Boolean(config.userRelated);
}

export function getUserRelatedColumn(
  tableName: EnabledTableKeys,
): string | null {
  const config = getTableConfig(tableName);
  if (typeof config.userRelated === "string") {
    return config.userRelated;
  }
  if (config.userRelated === true) {
    return "userId";
  }
  return null;
}
