"use client";

import { useState, useEffect } from "react";

const MOBILE_BREAKPOINT = 768;

/**
 * A hook to determine if the current viewport is mobile-sized.
 * It safely handles server-side rendering by initially returning false,
 * and then updating to the correct value on the client after mounting.
 * This prevents hydration errors.
 * @returns {boolean} - True if the viewport width is less than 768px, otherwise false.
 */
export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    checkDevice();

    window.addEventListener("resize", checkDevice);

    return () => {
      window.removeEventListener("resize", checkDevice);
    };
  }, []);

  return isMobile;
}
