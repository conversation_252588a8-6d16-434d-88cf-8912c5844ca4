import env from "@/env";
import * as tables from "../tables";
import {
  getConnectionConfig,
  validateDatabaseConfig,
} from "@/database/engine/engine-config";

import type { DBEngine } from "@/types/db";
const engine = (env.DATABASE_ENGINE || "postgres") as DBEngine;
const databaseUrl = env.DATABASE_URL;
const connectionConfig = getConnectionConfig();

// In dev, ensure env & config are correct
if (process.env.NODE_ENV === "development") {
  validateDatabaseConfig();
}

let db: any;
let sql: any;
let closeDatabase: () => Promise<void>;

// Dynamic engine setup
switch (engine) {
  // -------------------- POSTGRES --------------------
  case "postgres": {
    const [{ drizzle }, postgres] = await Promise.all([
      import("drizzle-orm/postgres-js"),
      import("postgres"),
    ]);

    // postgres import interop
    const pg = (postgres as any).default ?? postgres;

    sql = pg(databaseUrl, connectionConfig);
    db = drizzle(sql, { schema: { ...tables } });

    closeDatabase = async () => {
      await sql.end({ timeout: 5 });
    };

    break;
  }

  // -------------------- MYSQL --------------------
  case "mysql": {
    const [{ drizzle }, mysql] = await Promise.all([
      import("drizzle-orm/mysql2"),
      import("mysql2/promise"),
    ]);

    const mysqlModule: any = mysql;

    // Prefer a pool for mysql. Support passing a connection URI or options.
    if (typeof databaseUrl === "string" && databaseUrl.length > 0) {
      try {
        sql = mysqlModule.createPool(databaseUrl);
      } catch (e) {
        // fallback to object form
        sql = mysqlModule.createPool({ uri: databaseUrl, ...(connectionConfig || {}) });
      }
    } else {
      sql = mysqlModule.createPool({ ...(connectionConfig || {}) });
    }

    db = drizzle(sql, { schema: { ...tables }, mode: "default" });

    closeDatabase = async () => {
      await sql.end();
    };

    break;
  }

  // -------------------- SQLITE --------------------
  case "sqlite": {
    const [{ drizzle }, sqlite3] = await Promise.all([
      import("drizzle-orm/better-sqlite3"),
      import("better-sqlite3"),
    ]);

    const sqlitePath = databaseUrl?.replace("file:", "") || ":memory:";

    // better-sqlite3 import interop
    const SqliteLib: any = (sqlite3 as any).default ?? sqlite3;

    // connectionConfig isn't directly passed to better-sqlite3; pass only accepted options
    const sqliteOpts: any = {};
    sql = new SqliteLib(sqlitePath, sqliteOpts);
    db = drizzle(sql, { schema: { ...tables } });

    closeDatabase = async () => {
      sql.close();
    };

    break;
  }

  default:
    throw new Error(`Unsupported DB engine: ${engine}`);
}

export { db, sql, closeDatabase };
