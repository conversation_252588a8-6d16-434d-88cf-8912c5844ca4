"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { signIn } from "@/lib/auth/auth-client";
import { authValidation } from "@/validation/auth";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Link from "next/link";
import { toast } from "sonner";
import { Mail } from "lucide-react";
import { useRouter } from "next/navigation";
import { getAvailableSocialProviders } from "@/lib/auth/providers";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import { SocialLoginButtons } from "@/app/(auth)/_components/social-login-buttons";

export function SignupForm({
  availableProviders = getAvailableSocialProviders(),
}: {
  availableProviders?: ReturnType<typeof getAvailableSocialProviders>;
}) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof authValidation>>({
    resolver: zodResolver(authValidation),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: z.infer<typeof authValidation>) => {
    setLoading(true);
    const result = await signIn.magicLink({
      email: data.email,
      callbackURL: "/dashboard",
    });

    if (result.error) {
      toast.error(result.error.message);
      setLoading(false);
      return;
    }

    const params = new URLSearchParams({ email: data.email });
    router.push(`/auth/sent?${params.toString()}`);
  };

  const config = {
    title: "Get started today",
    description: "Create your account in seconds with just your email address",
    badgeText: "Get started",
    submitButtonText: "Create Account",
    loadingText: "Sending magic link...",
    submitIcon: Mail,
    alternativeActionText: "Already have an account?",
    alternativeActionLink: (
      <Link
        href="/login"
        className="text-primary hover:text-primary/80 cursor-pointer font-medium underline-offset-4 transition-colors hover:underline"
      >
        Sign in instead
      </Link>
    ),
    showTerms: true,
    callbackURL: "/dashboard",
  };

  const fields = [
    {
      name: "email" as const,
      label: "Email address",
      placeholder: "<EMAIL>",
      icon: Mail,
      type: "email",
    },
  ];

  const handleSubmit = async (data: z.infer<typeof authValidation>) => {
    try {
      setLoading(true);
      await onSubmit(data);
    } catch {
      toast.error("Something went wrong. Contact support if the issue persists");
      setLoading(false);
    }
  };

  return (
    <Card className="bg-background/80 max-w-md shadow-xl backdrop-blur-sm">
      <CardHeader className="space-y-4 pb-2">
        <div className="flex justify-center">
          <Badge
            variant="secondary"
            className="bg-primary/10 text-primary border-primary/20"
          >
            <Sparkles className="mr-1 h-3 w-3" />
            {config.badgeText}
          </Badge>
        </div>

        <div className="space-y-2 text-center">
          <CardTitle className="from-foreground to-foreground/70 bg-gradient-to-r bg-clip-text text-2xl font-bold text-transparent md:text-3xl">
            {config.title}
          </CardTitle>
          <CardDescription className="text-muted-foreground text-sm md:text-base">
            {config.description}
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {availableProviders && availableProviders.length > 0 && (
              <>
                <SocialLoginButtons
                  callbackURL={config.callbackURL}
                  availableProviders={availableProviders}
                  loading={loading}
                  onLoadingChange={setLoading}
                />

                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <span className="border-border w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background text-muted-foreground px-3 font-medium">
                      Or continue with magic link
                    </span>
                  </div>
                </div>
              </>
            )}

            {fields.map((field) => {
              const IconComponent = field.icon;
              return (
                <FormField
                  key={field.name}
                  control={form.control}
                  name={field.name}
                  render={({ field: formField }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-foreground text-sm font-medium">
                        {field.label}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <IconComponent className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                          <Input
                            placeholder={field.placeholder}
                            type={field.type || "text"}
                            {...formField}
                            disabled={loading}
                            className="focus:border-primary/50 h-12 border-2 pl-10 transition-colors"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              );
            })}

            <Button
              type="submit"
              disabled={loading}
              className="from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground h-12 w-full cursor-pointer bg-gradient-to-r font-medium shadow-lg transition-all duration-200 hover:shadow-xl"
            >
              {loading ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>{config.loadingText}</span>
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <config.submitIcon className="h-4 w-4" />
                  <span>{config.submitButtonText}</span>
                  <ArrowRight className="h-4 w-4" />
                </span>
              )}
            </Button>

            <div className="pt-4 text-center">
              <p className="text-muted-foreground text-sm">
                {config.alternativeActionText} {config.alternativeActionLink}
              </p>
            </div>
          </form>
        </Form>

        {config.showTerms && (
          <div className="border-border/50 border-t pt-4">
            <p className="text-muted-foreground/70 text-center text-xs leading-relaxed">
              By creating an account, you agree to our{" "}
              <Link
                href="/terms"
                className="text-primary hover:text-primary/80 cursor-pointer font-medium underline-offset-4 transition-colors hover:underline"
              >
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link
                href="/privacy"
                className="text-primary hover:text-primary/80 cursor-pointer font-medium underline-offset-4 transition-colors hover:underline"
              >
                Privacy Policy
              </Link>
              .
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
