export interface TableConfig {
  userRelated?: boolean | string;
  hiddenColumns?: string[];
  readOnlyColumns?: string[];
}

export type ColumnType =
  | "string"
  | "number"
  | "boolean"
  | "date"
  | "enum"
  | "json"
  | "uuid"
  | "text"
  | "user_id"
  | "email"
  | "url"
  | "phone"
  | "color"
  | "file"
  | "image"
  | "richtext"
  | "markdown"
  | "tags"
  | "currency"
  | "foreign_key"
  | "textarea"
  | "password"
  | "filesize";

export interface ColumnInfo {
  name: string;
  type: ColumnType;
  isOptional: boolean;
  isPrimaryKey: boolean;
  isAutoGenerated: boolean;
  enumValues?: string[];
  foreignKey?: {
    table: string;
    column: string;
    displayField?: string;
  };
}

export type SchemaInfo = ColumnInfo[];
