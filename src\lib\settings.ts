import fs from "fs/promises";
import path from "path";
import { mkdir } from "fs/promises";
import { db } from "@/database";
import { eq } from "drizzle-orm";
import { get as getEdgeConfig } from "@vercel/edge-config";
import { settings as settingsTable } from "@/database/schema";
import type { KVNamespace, SettingsBackend } from "@/types/settings";
import env from "@/env";

declare const APP_SETTINGS: KVNamespace; // Cloudflare KV (if on CF Workers)

const SETTINGS_KEY = "settings";
const SETTINGS_FILE = path.join(process.cwd(), "config/settings.json");
const SETTINGS_DIR = path.join(process.cwd(), "config/settings");
const backend = (env.SETTINGS_BACKEND || "file") as SettingsBackend;
const BACKUP_DB = env.SETTINGS_DB_BACKUP === true;

let cache: Record<string, any> = {};
let cacheTime: Record<string, number> = {};
const CACHE_TTL_MS = 60_000; // 1 minute

function makeKey(section?: string) {
  return section ? `${SETTINGS_KEY}:${section}` : SETTINGS_KEY;
}

export async function getSettings(force = false, section?: string): Promise<any> {
  const cacheKey = section || "__default__";
  if (!force && cache[cacheKey] && Date.now() - (cacheTime[cacheKey] || 0) < CACHE_TTL_MS) {
    return cache[cacheKey];
  }

  let data: any = {};
  const key = makeKey(section);

  try {
    switch (backend) {
      case "kv":
        data = await getFromKV(key);
        break;
      case "vercel":
        data = await getFromVercelEdgeConfig(key);
        break;
      case "file":
        data = await getFromFile(section);
        break;
      case "db":
        data = await getFromDB(key);
        break;
      default:
        throw new Error(`Unknown settings backend: ${backend}`);
    }
  } catch (err) {
    console.error(`[Settings] Read error from ${backend}:`, err);
    data = {};
  }

  cache[cacheKey] = data;
  cacheTime[cacheKey] = Date.now();
  return data;
}

export async function updateSettings(newSettings: any, section?: string): Promise<void> {
  const key = makeKey(section);
  // 1. Save to primary backend
  switch (backend) {
    case "kv":
      await saveToKV(key, newSettings);
      break;
    case "vercel":
      await saveToVercelEdgeConfig(key, newSettings);
      break;
    case "file": // Edge runtimes don’t support file-based settings storage
      await saveToFile(section, newSettings);
      break;
    case "db":
      await saveToDB(key, newSettings);
      break;
    default:
      throw new Error(`Unknown settings backend: ${backend}`);
  }

  // 2. Optional backup to DB
  if (BACKUP_DB && backend !== "db") {
    await saveToDB(key, newSettings);
  }

  // 3. Update cache
  const cacheKey = section || "__default__";
  cache[cacheKey] = newSettings;
  cacheTime[cacheKey] = Date.now();
}

async function getFromKV(key: string) {
  try {
    const data = await APP_SETTINGS.get(key, "json");
    return data || {};
  } catch (err) {
    console.error("[Settings] KV read error:", err);
    return {};
  }
}

async function saveToKV(key: string, settings: any) {
  try {
    await APP_SETTINGS.put(key, JSON.stringify(settings));
  } catch (err) {
    console.error("[Settings] KV write error:", err);
  }
}

async function getFromVercelEdgeConfig(key: string) {
  try {
    const data = await getEdgeConfig(key);
    return data || {};
  } catch (err) {
    console.error("[Settings] Vercel Edge Config read error:", err);
    return {};
  }
}

async function saveToVercelEdgeConfig(_key: string, _: any) {
  console.warn(
    "[Settings] Vercel Edge Config is read-only at runtime. Use dashboard/API to update."
  );
}

async function getFromFile(section?: string) {
  try {
    const file = section ? path.join(SETTINGS_DIR, `${section}.json`) : SETTINGS_FILE;
    const json = await fs.readFile(file, "utf-8");
    return JSON.parse(json);
  } catch (err: any) {
    if (err.code === "ENOENT") return {};
    console.error("[Settings] File read error:", err);
    return {};
  }
}

async function saveToFile(section: string | undefined, settings: any) {
  try {
    if (section) {
      await mkdir(SETTINGS_DIR, { recursive: true });
      const file = path.join(SETTINGS_DIR, `${section}.json`);
      await fs.writeFile(file, JSON.stringify(settings, null, 2), "utf-8");
    } else {
      const dir = path.dirname(SETTINGS_FILE);
      await mkdir(dir, { recursive: true });
      await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2), "utf-8");
    }
  } catch (err) {
    console.error("[Settings] File write error:", err);
  }
}

async function getFromDB(key: string) {
  try {
    const record = await db
      .select()
      .from(settingsTable)
      .where(eq(settingsTable.key, key))
      .limit(1);

    if (!record.length) return {};
    return JSON.parse(record[0].value);
  } catch (err) {
    console.error("[Settings] DB read error:", err);
    return {};
  }
}

async function saveToDB(key: string, settings: any) {
  const json = JSON.stringify(settings);
  try {
    await db
      .insert(settingsTable)
      .values({
        key,
        value: json,
      })
      .onConflictDoUpdate({
        target: settingsTable.key,
        set: {
          value: json,
          updatedAt: new Date(),
        },
      });
  } catch (err) {
    console.error("[Settings] DB write error:", err);
  }
}
