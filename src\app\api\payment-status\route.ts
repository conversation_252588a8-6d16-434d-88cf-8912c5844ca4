import { auth } from "@/lib/auth/auth";
import { getUserSubscription } from "@/lib/database/subscription";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId =
      searchParams.get("sessionId") || searchParams.get("checkout_id");

    if (!sessionId) {
      return NextResponse.json(
        { error: "Session ID is required" },
        { status: 400 },
      );
    }

    const session = await auth.api.getSession({ headers: request.headers });
    const userId = session?.user?.id;

    const subscription = userId ? await getUserSubscription(userId) : null;

    if (subscription) {
      if (
        subscription.status === "active" ||
        subscription.status === "trialing"
      ) {
        return NextResponse.json({
          status: "success",
          subscription,
          message: "Payment successful and subscription is active",
        });
      } else if (
        subscription.status === "past_due" ||
        subscription.status === "unpaid"
      ) {
        return NextResponse.json({
          status: "failed",
          subscription,
          message: "Payment failed or subscription is past due",
        });
      } else if (subscription.status === "canceled") {
        // If there's a sessionId, it means this is a payment flow
        if (sessionId) {
          return NextResponse.json({
            status: "pending",
            message: "Payment is being processed",
            sessionId,
          });
        }
        return NextResponse.json({
          status: "cancelled",
          subscription,
          message: "Subscription has been cancelled",
        });
      }
    }

    if (sessionId) {
      try {
        const { creemClient, creemApiKey } = await import(
          "@/lib/billing/creem/client"
        );

        const checkoutResponse = await creemClient.retrieveCheckout({
          xApiKey: creemApiKey,
          checkoutId: sessionId,
        });

        if (checkoutResponse?.status) {
          switch (checkoutResponse.status) {
            case "completed":
              return NextResponse.json({
                status: "success",
                message: "Payment completed successfully",
                sessionId,
              });
            case "failed":
              return NextResponse.json({
                status: "failed",
                message: "Payment failed",
                sessionId,
              });
            case "canceled":
              return NextResponse.json({
                status: "cancelled",
                message: "Payment was cancelled",
                sessionId,
              });
            case "pending":
            case "processing":
            default:
              return NextResponse.json({
                status: "pending",
                message:
                  "Payment is being processed. This may take a few minutes.",
                sessionId,
              });
          }
        }

        return NextResponse.json({
          status: "pending",
          message: "Payment is being processed. This may take a few minutes.",
          sessionId,
        });
      } catch (error) {
        console.error("Error checking Creem payment status:", error);
        return NextResponse.json({
          status: "pending",
          message: "Payment status is being verified. Please wait a moment.",
          sessionId,
        });
      }
    }

    return NextResponse.json({
      status: "pending",
      message: "Payment status is being verified",
    });
  } catch (error) {
    console.error("[Payment Status API Error]", error);
    return NextResponse.json(
      { error: "Failed to check payment status" },
      { status: 500 },
    );
  }
}
