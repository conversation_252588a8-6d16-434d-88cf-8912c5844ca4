"use client";

import { useEffect } from "react";
import { useSession } from "@/lib/auth/auth-client";
import { usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";
import Loading from "@/app/loading";

export function SessionGuard({ children }: { children: React.ReactNode }) {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (isPending) {
      return;
    }

    if (!session && pathname.startsWith("/dashboard")) {
      toast.error("Your session has expired. Please log in again.");
      router.push("/login");
    }
  }, [session, isPending, router, pathname]);

  if (isPending) {
    return <Loading />;
  }

  if (session) {
    return <>{children}</>;
  }

  return null;
}
