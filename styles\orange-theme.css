html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
@layer base {
	button:not(:disabled),
		[role="button"]:not(:disabled) {
		cursor: pointer;
	}
}

[role="menuitem"]:not(:disabled) {
	cursor: pointer;
}

:root {
	--warning: hsl(38 92% 50%);
	--warning-foreground: hsl(48 96% 89%);
}

.dark {
	--warning: hsl(48 96% 89%);
	--warning-foreground: hsl(38 92% 50%);
}

@theme inline {
	--color-warning: var(--warning);
	--color-warning-foreground: var(--warning-foreground);
}


.shadow-light {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.085);
}

.shadow-dark {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.141);
}

/** iOS Dynamic System Font Scaling */
/* @supports (-webkit-touch-callout:none) {
	html {
		font: -apple-system-body;
	}
} */

/* Marquee animations */
@keyframes marquee {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-100%);
  }
}
@keyframes marquee-vertical {
  from {
    transform: translateY(0%);
  }
  to {
    transform: translateY(-100%);
  }
}
@layer utilities {
  .animate-marquee {
    animation: marquee var(--duration) linear infinite;
  }
  
  .animate-marquee-vertical {
    animation: marquee-vertical var(--duration) linear infinite;
  }
}

::selection {
  background: var(--primary);
  color: black;
  -webkit-text-fill-color: black;
}
::-moz-selection {
  background: var(--primary);
  color: black;
  -webkit-text-fill-color: black;
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.1884 0.0128 248.5103);
  --card: oklch(0.9784 0.0011 197.1387);
  --card-foreground: oklch(0.1884 0.0128 248.5103);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1884 0.0128 248.5103);
  --primary: oklch(0.7576 0.1590 55.9344);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.1884 0.0128 248.5103);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.9222 0.0013 286.3737);
  --muted-foreground: oklch(0.1884 0.0128 248.5103);
  --accent: oklch(0.9392 0.0166 250.8453);
  --accent-foreground: oklch(0.7576 0.1590 55.9344);
  --destructive: oklch(0.6188 0.2376 25.7658);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9317 0.0118 231.6594);
  --input: oklch(0.9809 0.0025 228.7836);
  --ring: oklch(0.7576 0.1590 55.9344);
  --chart-1: oklch(0.7576 0.1590 55.9344);
  --chart-2: oklch(0.6907 0.1554 160.3454);
  --chart-3: oklch(0.8214 0.1600 82.5337);
  --chart-4: oklch(0.7064 0.1822 151.7125);
  --chart-5: oklch(0.5919 0.2186 10.5826);
  --sidebar: oklch(0.9784 0.0011 197.1387);
  --sidebar-foreground: oklch(0.1884 0.0128 248.5103);
  --sidebar-primary: oklch(0.7576 0.1590 55.9344);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9392 0.0166 250.8453);
  --sidebar-accent-foreground: oklch(0.7576 0.1590 55.9344);
  --sidebar-border: oklch(0.9271 0.0101 238.5177);
  --sidebar-ring: oklch(0.7576 0.1590 55.9344);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: 0.75rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1469 0.0041 49.2499);
  --foreground: oklch(0.9699 0.0013 106.4238);
  --card: oklch(0.2161 0.0061 56.0434);
  --card-foreground: oklch(0.9699 0.0013 106.4238);
  --popover: oklch(0.2161 0.0061 56.0434);
  --popover-foreground: oklch(0.9699 0.0013 106.4238);
  --primary: oklch(0.7576 0.1590 55.9344);
  --primary-foreground: oklch(0.2095 0.0163 71.0606);
  --secondary: oklch(0.2686 0 0);
  --secondary-foreground: oklch(0.9699 0.0013 106.4238);
  --muted: oklch(0.2685 0.0063 34.2976);
  --muted-foreground: oklch(0.7161 0.0091 56.2590);
  --accent: oklch(0.2686 0 0);
  --accent-foreground: oklch(0.9699 0.0013 106.4238);
  --destructive: oklch(0.4437 0.1613 26.8994);
  --destructive-foreground: oklch(0.9356 0.0309 17.7172);
  --border: oklch(0.2685 0.0063 34.2976);
  --input: oklch(0.2685 0.0063 34.2976);
  --ring: oklch(0.7576 0.1590 55.9344);
  --chart-1: oklch(0.7049 0.1867 47.6044);
  --chart-2: oklch(0.6231 0.1880 259.8145);
  --chart-3: oklch(0.6271 0.1699 149.2138);
  --chart-4: oklch(0.8606 0.1731 91.9357);
  --chart-5: oklch(0.5575 0.2525 302.3212);
  --sidebar: oklch(0.1928 0.0051 67.5172);
  --sidebar-foreground: oklch(0.9232 0.0026 48.7171);
  --sidebar-primary: oklch(0.7576 0.1590 55.9344);
  --sidebar-primary-foreground: oklch(0.2161 0.0061 56.0434);
  --sidebar-accent: oklch(0.2686 0 0);
  --sidebar-accent-foreground: oklch(0.9699 0.0013 106.4238);
  --sidebar-border: oklch(0.2685 0.0063 34.2976);
  --sidebar-ring: oklch(0.7576 0.1590 55.9344);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: 0.75rem;
  --shadow-2xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.30);
  --shadow-xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.30);
  --shadow-sm: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60);
  --shadow: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60);
  --shadow-md: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 2px 4px -1px hsl(0 0% 0% / 0.60);
  --shadow-lg: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 4px 6px -1px hsl(0 0% 0% / 0.60);
  --shadow-xl: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 8px 10px -1px hsl(0 0% 0% / 0.60);
  --shadow-2xl: 0px 2px 5px 0px hsl(0 0% 0% / 1.50);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}