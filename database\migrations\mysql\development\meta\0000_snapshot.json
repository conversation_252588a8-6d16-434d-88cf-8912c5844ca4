{"version": "5", "dialect": "mysql", "id": "20329a0e-1d5b-411d-8552-245182113f70", "prevId": "********-0000-0000-0000-************", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "accountId": {"name": "accountId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerId": {"name": "providerId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"accounts_userId_idx": {"name": "accounts_userId_idx", "columns": ["userId"], "isUnique": false}}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_id": {"name": "accounts_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "char(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customerId": {"name": "customerId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriptionId": {"name": "subscriptionId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false, "autoincrement": false}, "productId": {"name": "productId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "paymentId": {"name": "paymentId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'usd'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "paymentType": {"name": "paymentType", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {"payments_userId_idx": {"name": "payments_userId_idx", "columns": ["userId"], "isUnique": false}}, "foreignKeys": {"payments_userId_users_id_fk": {"name": "payments_userId_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"payments_id": {"name": "payments_id", "columns": ["id"]}}, "uniqueConstraints": {"payments_paymentId_unique": {"name": "payments_paymentId_unique", "columns": ["paymentId"]}}, "checkConstraint": {}}, "rate_limits": {"name": "rate_limits", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "count": {"name": "count", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "lastRequest": {"name": "lastRequest", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"rate_limits_id": {"name": "rate_limits_id", "columns": ["id"]}}, "uniqueConstraints": {"rate_limits_key_unique": {"name": "rate_limits_key_unique", "columns": ["key"]}}, "checkConstraint": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false, "autoincrement": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "os": {"name": "os", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "browser": {"name": "browser", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "deviceType": {"name": "deviceType", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sessions_id": {"name": "sessions_id", "columns": ["id"]}}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "columns": ["token"]}}, "checkConstraint": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"settings_key": {"name": "settings_key", "columns": ["key"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "subscriptions": {"name": "subscriptions", "columns": {"id": {"name": "id", "type": "char(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customerId": {"name": "customerId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriptionId": {"name": "subscriptionId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "productId": {"name": "productId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceledAt": {"name": "canceledAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {"subscriptions_userId_idx": {"name": "subscriptions_userId_idx", "columns": ["userId"], "isUnique": false}, "subscriptions_customerId_idx": {"name": "subscriptions_customerId_idx", "columns": ["customerId"], "isUnique": false}}, "foreignKeys": {"subscriptions_userId_users_id_fk": {"name": "subscriptions_userId_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"subscriptions_id": {"name": "subscriptions_id", "columns": ["id"]}}, "uniqueConstraints": {"subscriptions_subscriptionId_unique": {"name": "subscriptions_subscriptionId_unique", "columns": ["subscriptionId"]}}, "checkConstraint": {}}, "uploads": {"name": "uploads", "columns": {"id": {"name": "id", "type": "char(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileKey": {"name": "fileKey", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileName": {"name": "fileName", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileSize": {"name": "fileSize", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "contentType": {"name": "contentType", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {"uploads_userId_idx": {"name": "uploads_userId_idx", "columns": ["userId"], "isUnique": false}, "uploads_fileKey_idx": {"name": "uploads_fileKey_idx", "columns": ["fileKey"], "isUnique": false}}, "foreignKeys": {"uploads_userId_users_id_fk": {"name": "uploads_userId_users_id_fk", "tableFrom": "uploads", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"uploads_id": {"name": "uploads_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "paymentProviderCustomerId": {"name": "paymentProviderCustomerId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"]}, "users_paymentProviderCustomerId_unique": {"name": "users_paymentProviderCustomerId_unique", "columns": ["paymentProviderCustomerId"]}}, "checkConstraint": {}}, "verifications": {"name": "verifications", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verifications_id": {"name": "verifications_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "webhook_events": {"name": "webhook_events", "columns": {"id": {"name": "id", "type": "char(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventId": {"name": "eventId", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventType": {"name": "eventType", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'creem'"}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "processedAt": {"name": "processedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {"webhook_events_eventId_idx": {"name": "webhook_events_eventId_idx", "columns": ["eventId"], "isUnique": false}, "webhook_events_provider_idx": {"name": "webhook_events_provider_idx", "columns": ["provider"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"webhook_events_id": {"name": "webhook_events_id", "columns": ["id"]}}, "uniqueConstraints": {"webhook_events_eventId_unique": {"name": "webhook_events_eventId_unique", "columns": ["eventId"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}