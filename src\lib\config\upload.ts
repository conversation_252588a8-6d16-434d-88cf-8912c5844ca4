
import { z } from "zod";

const MIME_TYPE_TO_EXTENSION = {
  "image/jpeg": "jpeg",
  "image/png": "png",
  "image/gif": "gif",
  "image/webp": "webp",
  "image/svg+xml": "svg",
  "image/apng": "apng",
  "image/avif": "avif",
  "image/bmp": "bmp",
  "image/x-icon": "ico",
  "image/tiff": "tiff",
  "image/vnd.microsoft.icon": "ico",

  "application/pdf": "pdf",
  "application/msword": "doc",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "docx",
  "application/vnd.ms-excel": "xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "application/vnd.ms-powerpoint": "ppt",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    "pptx",

  "audio/mpeg": "mp3",
  "audio/ogg": "ogg",
  "audio/wav": "wav",
  "audio/mp4": "m4a",
  "audio/opus": "opus",
  "audio/webm": "webm",
  "video/mp4": "mp4",
  "video/webm": "webm",
  "video/ogg": "ogv",
  "video/x-matroska": "mkv",
  "video/x-flv": "flv",
  "video/quicktime": "mov",
  "video/x-msvideo": "avi",
  "video/x-ms-wmv": "wmv",

  "text/plain": "txt",
  "text/csv": "csv",
  "text/markdown": "md",
  "text/html": "html",
  "text/css": "css",
  "text/javascript": "js",
  "application/json": "json",
  "application/xml": "xml",
  "text/xml": "xml",
  "text/calendar": "ics",

  "application/zip": "zip",
  "application/x-rar-compressed": "rar",
  "application/x-7z-compressed": "7z",
} as const;

export const UPLOAD_CONFIG = {

  MAX_FILE_SIZE: 50 * 1024 * 1024,

  MAX_FILE_SIZE_MB: 50,

  PRESIGNED_URL_EXPIRATION: 15 * 60,

  ALLOWED_FILE_TYPES: Object.keys(
    MIME_TYPE_TO_EXTENSION,
  ) as (keyof typeof MIME_TYPE_TO_EXTENSION)[],
} as const;

export function isFileTypeAllowed(contentType: string): boolean {
  return UPLOAD_CONFIG.ALLOWED_FILE_TYPES.includes(
    contentType as (typeof UPLOAD_CONFIG.ALLOWED_FILE_TYPES)[number],
  );
}

export function isFileSizeAllowed(size: number): boolean {
  return size <= UPLOAD_CONFIG.MAX_FILE_SIZE;
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export function getFileExtension(contentType: string): string {
  if (contentType in MIME_TYPE_TO_EXTENSION) {
    return MIME_TYPE_TO_EXTENSION[
      contentType as keyof typeof MIME_TYPE_TO_EXTENSION
    ];
  }

  const parts = contentType.split("/");
  const subtype = parts[1];
  if (subtype && !subtype.includes("*")) {
    const ext = subtype.split("+")[0];
    return ext.toLowerCase();
  }

  return "bin";
}

export const presignedUrlRequestSchema = z.object({
  fileName: z
    .string()
    .min(1, "File name cannot be empty.")
    .max(255, "File name is too long."),
  contentType: z.string().min(1, "Content type cannot be empty."),
  size: z.number().positive("File size must be positive."),
});
